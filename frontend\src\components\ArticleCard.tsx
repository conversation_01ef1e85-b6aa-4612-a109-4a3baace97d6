import { FC } from 'react'
import { motion } from 'framer-motion'

interface ArticleCardProps {
  title: string
  coverImage: string
  author: {
    name: string
    avatar: string
  }
  publishDate: string
  stats: {
    likes: number
    comments: number
    bookmarks: number
  }
}

const ArticleCard: FC<ArticleCardProps> = ({
  title,
  coverImage,
  author,
  publishDate,
  stats
}) => {
  return (
    <motion.div
      className="card w-[300px] h-[420px] overflow-hidden"
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <div className="relative h-[180px] w-full overflow-hidden">
        <img
          src={coverImage}
          alt={title}
          className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-300"
        />
      </div>
      
      <div className="p-4">
        <h3 className="font-montserrat font-bold text-lg text-text-primary line-clamp-2 mb-3">
          {title}
        </h3>
        
        <div className="flex items-center mb-4">
          <img
            src={author.avatar}
            alt={author.name}
            className="w-8 h-8 rounded-full mr-3"
          />
          <div>
            <p className="text-sm font-medium text-text-primary">{author.name}</p>
            <p className="text-xs text-text-secondary">{publishDate}</p>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-text-secondary">
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18l-1.45-1.32C3.4 12.36 0 9.28 0 5.5 0 2.42 2.42 0 5.5 0 7.24 0 8.91.81 10 2.09 11.09.81 12.76 0 14.5 0 17.58 0 20 2.42 20 5.5c0 3.78-3.4 6.86-8.55 11.54L10 18z"/>
            </svg>
            <span className="text-sm">{stats.likes}</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M18 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2z"/>
            </svg>
            <span className="text-sm">{stats.comments}</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"/>
            </svg>
            <span className="text-sm">{stats.bookmarks}</span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default ArticleCard
