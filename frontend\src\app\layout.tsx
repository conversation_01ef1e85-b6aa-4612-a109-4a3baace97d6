import { Metadata } from 'next'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import '../styles/globals.css'

export const metadata: Metadata = {
  title: 'OneNews - 分享你的故事',
  description: 'OneNews是一个现代化的创作平台，让每个人都能分享自己的故事和想法。',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow mt-16">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}
