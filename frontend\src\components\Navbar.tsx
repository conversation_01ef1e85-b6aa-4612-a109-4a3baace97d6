import { FC } from 'react'
import Link from 'next/link'
import Image from 'next/image'

const Navbar: FC = () => {
  return (
    <nav className="fixed top-0 left-0 w-full bg-white shadow-md z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link href="/" className="flex items-center space-x-2">
            <span className="font-montserrat text-2xl font-bold text-primary transform -skew-x-6">
              OneNews
            </span>
          </Link>

          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="nav-link">首页</Link>
            <Link href="/explore" className="nav-link">探索</Link>
            <Link href="/post" className="nav-link">发布作品</Link>
            <Link href="/profile" className="nav-link">个人中心</Link>
          </div>

          <div className="flex items-center space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="搜索..."
                className="input w-48 pl-10"
              />
              <svg
                className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <button className="btn-primary">登录/注册</button>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
