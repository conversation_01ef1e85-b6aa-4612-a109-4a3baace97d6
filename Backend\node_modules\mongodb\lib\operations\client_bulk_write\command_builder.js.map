{"version": 3, "file": "command_builder.js", "sourceRoot": "", "sources": ["../../../src/operations/client_bulk_write/command_builder.ts"], "names": [], "mappings": ";;;AAkdA,wCAoBC;AAteD,qCAA4E;AAC5E,kDAAuD;AACvD,uCAAuE;AAGvE,qCAAyD;AACzD,uCAAqE;AA0BrE;;GAEG;AACH,MAAM,sBAAsB,GAAG,IAAI,CAAC;AAEpC,gBAAgB;AAChB,MAAa,6BAA6B;IAaxC;;;OAGG;IACH,YACE,MAAwD,EACxD,OAA+B,EAC/B,SAAqB;QAErB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,0BAAkB,CAAC;QACjD,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,IAAI,UAAU;QACZ,IAAI,gBAAgB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QACtC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IACrD,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,UAAU,CACR,mBAA2B,EAC3B,iBAAyB,EACzB,iBAAyB;QAEzB,0EAA0E;QAC1E,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,qBAAqB,GAAG,CAAC,CAAC;QAC9B,MAAM,OAAO,GAA2B,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3D,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC7C,sEAAsE;QACtE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAEjD,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAClD,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC;YAC3B,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAEnC,mCAAmC;YACnC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAC/D,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAChC,CAAC;YAED,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,gEAAgE;gBAChE,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC/E,IAAI,eAAe,CAAC;gBACpB,IAAI,CAAC;oBACH,eAAe,GAAG,WAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBAC9C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,IAAI,iCAAyB,CAAC,uCAAuC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC1F,CAAC;gBAED,kBAAkB,CAAC,KAAK,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;gBAE9D,mEAAmE;gBACnE,oEAAoE;gBACpE,wEAAwE;gBACxE,IACE,aAAa,GAAG,eAAe,CAAC,MAAM,GAAG,mBAAmB;oBAC5D,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,iBAAiB,EAChD,CAAC;oBACD,+FAA+F;oBAC/F,aAAa,GAAG,sBAAsB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;oBACtF,+CAA+C;oBAC/C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACN,mEAAmE;oBACnE,uCAAuC;oBACvC,MAAM;gBACR,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,+EAA+E;gBAC/E,sDAAsD;gBACtD,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,qBAAqB,CAAC,CAAC;gBAC1C,MAAM,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;gBAC1B,MAAM,SAAS,GAAG,cAAc,CAC9B,KAAK,EACL,qBAAqB,EACrB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,CACb,CAAC;gBACF,IAAI,YAAY,CAAC;gBACjB,IAAI,eAAe,CAAC;gBACpB,IAAI,CAAC;oBACH,YAAY,GAAG,WAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACtC,eAAe,GAAG,WAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBAC9C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,IAAI,iCAAyB,CAAC,qCAAqC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBACxF,CAAC;gBAED,kBAAkB,CAAC,QAAQ,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;gBAC9D,kBAAkB,CAAC,KAAK,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;gBAE9D,4EAA4E;gBAC5E,sEAAsE;gBACtE,6EAA6E;gBAC7E,yBAAyB;gBACzB,IACE,aAAa,GAAG,YAAY,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,GAAG,mBAAmB;oBAClF,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,iBAAiB,EAChD,CAAC;oBACD,+FAA+F;oBAC/F,aAAa;wBACX,sBAAsB;4BACtB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;4BACzC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;oBAC/C,8DAA8D;oBAC9D,qBAAqB,EAAE,CAAC;oBACxB,+CAA+C;oBAC/C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACN,mEAAmE;oBACnE,uCAAuC;oBACvC,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QACD,kDAAkD;QAClD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;QAC5C,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,WAAW;QACjB,MAAM,OAAO,GAA2B;YACtC,SAAS,EAAE,CAAC;YACZ,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI;YACrC,GAAG,EAAE,IAAI,2BAAgB,CAAC,KAAK,CAAC;YAChC,MAAM,EAAE,IAAI,2BAAgB,CAAC,QAAQ,CAAC;SACvC,CAAC;QACF,iEAAiE;QACjE,IAAI,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,IAAI,EAAE,CAAC;YAClD,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC;QAC3E,CAAC;QACD,4CAA4C;QAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QACjC,CAAC;QAED,iEAAiE;QACjE,gDAAgD;QAChD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACzC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAnMD,sEAmMC;AAED,SAAS,kBAAkB,CAAC,IAAY,EAAE,MAAkB,EAAE,iBAAyB;IACrF,IAAI,MAAM,CAAC,MAAM,GAAG,iBAAiB,EAAE,CAAC;QACtC,MAAM,IAAI,iCAAyB,CACjC,+BAA+B,IAAI,cAAc,MAAM,CAAC,MAAM,wCAAwC,iBAAiB,EAAE,CAC1H,CAAC;IACJ,CAAC;AACH,CAAC;AAQD;;;;;GAKG;AACI,MAAM,uBAAuB,GAAG,CACrC,KAAqC,EACrC,KAAa,EACb,SAAoB,EACG,EAAE;IACzB,MAAM,QAAQ,GAA0B;QACtC,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,KAAK,CAAC,QAAQ;KACzB,CAAC;IACF,QAAQ,CAAC,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnE,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAXW,QAAA,uBAAuB,2BAWlC;AAWF;;;;;GAKG;AACI,MAAM,uBAAuB,GAAG,CACrC,KAAqC,EACrC,KAAa,EACH,EAAE;IACZ,OAAO,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC,CAAC;AALW,QAAA,uBAAuB,2BAKlC;AAEF;;;;;GAKG;AACI,MAAM,wBAAwB,GAAG,CACtC,KAAsC,EACtC,KAAa,EACH,EAAE;IACZ,OAAO,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACnD,CAAC,CAAC;AALW,QAAA,wBAAwB,4BAKnC;AAEF;;GAEG;AACH,SAAS,qBAAqB,CAC5B,KAAuE,EACvE,KAAa,EACb,KAAc;IAEd,MAAM,QAAQ,GAA0B;QACtC,MAAM,EAAE,KAAK;QACb,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK,CAAC,MAAM;KACrB,CAAC;IACF,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IAC7B,CAAC;IACD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IACvC,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAeD;;;;;GAKG;AACI,MAAM,uBAAuB,GAAG,CACrC,KAAqC,EACrC,KAAa,EACb,OAA6B,EACN,EAAE;IACzB,OAAO,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC7D,CAAC,CAAC;AANW,QAAA,uBAAuB,2BAMlC;AAEF;;;;;GAKG;AACI,MAAM,wBAAwB,GAAG,CACtC,KAAsC,EACtC,KAAa,EACb,OAA6B,EACN,EAAE;IACzB,OAAO,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC,CAAC;AANW,QAAA,wBAAwB,4BAMnC;AAEF;;;GAGG;AACH,SAAS,cAAc,CAAC,MAAgB,EAAE,OAA6B;IACrE,IAAI,CAAC,IAAA,0BAAkB,EAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC;QACzC,MAAM,IAAI,qBAAa,CACrB,0GAA0G,CAC3G,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAC5B,KAAuE,EACvE,KAAa,EACb,KAAc,EACd,OAA6B;IAE7B,yEAAyE;IACzE,6EAA6E;IAC7E,uEAAuE;IACvE,gDAAgD;IAChD,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACtC,MAAM,QAAQ,GAA0B;QACtC,MAAM,EAAE,KAAK;QACb,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,UAAU,EAAE,KAAK,CAAC,MAAM;KACzB,CAAC;IACF,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IAC7B,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACjC,CAAC;IACD,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;QACvB,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;IAC7C,CAAC;IACD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IACvC,CAAC;IACD,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;QACpD,QAAQ,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAcD;;;;;GAKG;AACI,MAAM,wBAAwB,GAAG,CACtC,KAAsC,EACtC,KAAa,EACc,EAAE;IAC7B,IAAI,IAAA,0BAAkB,EAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;QAC1C,MAAM,IAAI,qBAAa,CACrB,0GAA0G,CAC3G,CAAC;IACJ,CAAC;IAED,MAAM,QAAQ,GAA8B;QAC1C,MAAM,EAAE,KAAK;QACb,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,UAAU,EAAE,KAAK,CAAC,WAAW;KAC9B,CAAC;IACF,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IAC7B,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACjC,CAAC;IACD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IACvC,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;QACvB,QAAQ,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AA7BW,QAAA,wBAAwB,4BA6BnC;AAEF,gBAAgB;AAChB,SAAgB,cAAc,CAC5B,KAAwC,EACxC,KAAa,EACb,SAAoB,EACpB,OAA6B;IAE7B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,WAAW;YACd,OAAO,IAAA,+BAAuB,EAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAC1D,KAAK,WAAW;YACd,OAAO,IAAA,+BAAuB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC/C,KAAK,YAAY;YACf,OAAO,IAAA,gCAAwB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAChD,KAAK,WAAW;YACd,OAAO,IAAA,+BAAuB,EAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACxD,KAAK,YAAY;YACf,OAAO,IAAA,gCAAwB,EAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACzD,KAAK,YAAY;YACf,OAAO,IAAA,gCAAwB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;AACH,CAAC"}