@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap');

@layer base {
  html {
    font-family: 'Open Sans', system-ui, sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-blue hover:scale-105 transition-all duration-300;
  }
  
  .btn-secondary {
    @apply bg-secondary-orange text-white px-4 py-2 rounded-md hover:brightness-110 hover:scale-105 transition-all duration-300;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1;
  }
  
  .input {
    @apply border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }
}

/* 动画类 */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms ease-in;
}
