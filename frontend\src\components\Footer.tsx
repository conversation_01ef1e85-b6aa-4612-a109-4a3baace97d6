import { FC } from 'react'

const Footer: FC = () => {
  return (
    <footer className="bg-gray-800 text-gray-300 py-8">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between">
          <div className="mb-6 md:mb-0">
            <h3 className="text-xl font-montserrat font-bold mb-4">OneNews</h3>
            <p className="text-sm max-w-md">
              OneNews是一个现代化的创作平台，让每个人都能分享自己的故事和想法。
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
            <div>
              <h4 className="text-white font-montserrat font-semibold mb-4">关于我们</h4>
              <ul className="space-y-2">
                <li><a href="#" className="hover:text-white transition-colors">关于OneNews</a></li>
                <li><a href="#" className="hover:text-white transition-colors">加入我们</a></li>
                <li><a href="#" className="hover:text-white transition-colors">联系方式</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-montserrat font-semibold mb-4">服务</h4>
              <ul className="space-y-2">
                <li><a href="#" className="hover:text-white transition-colors">创作指南</a></li>
                <li><a href="#" className="hover:text-white transition-colors">帮助中心</a></li>
                <li><a href="#" className="hover:text-white transition-colors">反馈建议</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-montserrat font-semibold mb-4">法律</h4>
              <ul className="space-y-2">
                <li><a href="#" className="hover:text-white transition-colors">用户协议</a></li>
                <li><a href="#" className="hover:text-white transition-colors">隐私政策</a></li>
                <li><a href="#" className="hover:text-white transition-colors">版权保护</a></li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-8 pt-8 text-sm text-center">
          <p>&copy; {new Date().getFullYear()} OneNews. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
