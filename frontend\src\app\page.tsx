'use client'

import { FC } from 'react'
import { motion } from 'framer-motion'
import ArticleCard from '@/components/ArticleCard'

const Home: FC = () => {
  // 模拟数据
  const articles = [
    {
      title: '人工智能如何改变我们的生活？',
      coverImage: 'https://picsum.photos/300/180',
      author: {
        name: '科技先锋',
        avatar: 'https://i.pravatar.cc/100?u=1'
      },
      publishDate: '2025-06-30',
      stats: {
        likes: 128,
        comments: 32,
        bookmarks: 45
      }
    },
    // 添加更多文章...
  ]

  return (
    <div className="min-h-screen bg-background-light">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-blue to-primary-purple text-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="font-montserrat text-5xl font-bold mb-6">
              分享你的故事
            </h1>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              OneNews让每个人都能找到属于自己的声音。在这里，你可以分享你的故事、想法和创意。
            </p>
            <button className="btn-secondary text-lg px-8 py-3">
              开始创作
            </button>
          </motion.div>
        </div>
      </section>

      {/* Featured Articles */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="font-montserrat text-3xl font-bold text-text-primary mb-8">
            推荐阅读
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {articles.map((article, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <ArticleCard {...article} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="font-montserrat text-3xl font-bold text-text-primary mb-8">
            探索分类
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['科技', '文化', '艺术', '生活'].map((category, index) => (
              <motion.div
                key={index}
                className="relative h-40 rounded-lg overflow-hidden cursor-pointer"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <img
                  src={`https://picsum.photos/400/300?random=${index}`}
                  alt={category}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                  <span className="text-white font-montserrat text-xl font-bold">
                    {category}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
